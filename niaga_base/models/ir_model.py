# -*- coding: utf-8 -*-
# Copyright 2024 Imam <PERSON><PERSON><PERSON>
# License AGPL-3.0 or later (https://www.gnu.org/licenses/agpl.html).

import logging
from inspect import ismethod

from psycopg2 import Error

from odoo import api, models

_logger = logging.getLogger(__name__)


class Base(models.AbstractModel):
    _inherit = 'base'

    def _prepare_columns(self, list_vals):
        vals = isinstance(list_vals, list) and list_vals[0] or list_vals
        return list(vals.keys())

    def _prepare_values(self, list_vals):
        list_vals = isinstance(list_vals, dict) and [list_vals] or list_vals
        return [tuple(val.values()) for val in list_vals]

    def _format_values(self, list_vals):
        self = self.browse()
        values = []
        for vals in list_vals:
            updates = [('id', "nextval('%s')" % self._sequence)]
            if self._log_access:
                updates.append(('create_uid', '%s', self._uid))
                updates.append(('write_uid', '%s', self._uid))
                updates.append(('create_date', "(now() at time zone 'UTC')"))
                updates.append(('write_date', "(now() at time zone 'UTC')"))

            for name, val in vals.items():
                field = self._fields[name]
                if field.store and field.column_type:
                    column_val = field.convert_to_column(val, self, vals)
                    updates.append((name, field.column_format, column_val))
            values.append(updates)
        return values

    @api.multi
    def _recompute_all(self, columns=None):
        if columns is None:
            columns = []
        for line in self:
            for key in line._field_computed.keys():
                if (
                    key.store
                    and key.compute
                    and not line[key.name]
                    and key.name not in columns
                ):
                    if isinstance(key.compute, str):
                        getattr(line, key.compute)()
                    elif ismethod(key.compute):
                        key.compute(line)

    @api.model
    @api.returns('self', lambda value: value.id)
    def _create_raw(self, list_vals):
        self.check_access_rights('create')
        if not list_vals:
            return

        if isinstance(list_vals, dict):
            list_vals = [list_vals]

        values = self._format_values(list_vals)
        columns = [val[0] for val in values[0]]
        query = 'INSERT INTO {} ({}) VALUES {} RETURNING id'.format(
            self._table,
            ', '.join(columns),
            ', '.join('({})'.format(', '.join(v[1] for v in val)) for val in values),
        )
        params = []
        for val in values:
            for u in val:
                if len(u) > 2:
                    params.append(u[2])
        try:
            self._cr.execute(query, tuple(params))
        except Error as e:
            self._cr.rollback()
            _logger.info('an error occured while bulk create: %s', e.pgerror)
            raise e
        result = self._cr.fetchall()
        ids = [r[0] for r in result] if len(result) > 1 else result[0]
        self = self.browse(ids)
        self._recompute_all(columns)
        return self

    def _write_raw(self, list_vals):
        self.check_access_rights('write')
        if not list_vals:
            return

        if isinstance(list_vals, dict):
            list_vals = [list_vals]

        table_name = self._table
        list_columns = self._prepare_columns(list_vals)
        columns = ', '.join(list_columns)
        list_vals = self._prepare_values(list_vals)
        values = ', '.join(map(str, list_vals))

        set_val = ', '.join(
            ('{} = tmp.{}').format(column, column) for column in list_columns
        )
        if self._log_access:
            set_val += (
                f", write_uid = {self._uid}, write_date = now() at time zone 'UTC'"
            )

        query = (
            'UPDATE {} SET {} FROM (VALUES {}) AS tmp({}) WHERE tmp.id = {}.id;'
        ).format(table_name, set_val, values, columns, table_name)
        try:
            self._cr.execute(query)
        except Error as e:
            self._cr.rollback()
            _logger.info('an error occured while bulk write: %s', e.pgerror)
            raise e
        return True
