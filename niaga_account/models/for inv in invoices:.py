for inv in invoices:
    move = inv.move_id
    move_state = move.state
    move.state = 'draft'
    move_lines = move.line_ids.filtered(lambda l: not l.invoice_id)
    for line in inv.invoice_line_ids:
        price_unit = line._get_anglo_saxon_price_unit()
        price = price_unit * line.quantity
        if price_unit == 0 or price == 0:
            print(inv.number, line.name)
        for move_line in move_lines.filtered(
            lambda l: l.product_id.id == line.product_id.id
        ):
            move_line = move_line.with_context(check_move_validity=False)
            if move_line.credit > 0.0:
                move_line.write({'credit': price})
            elif move_line.debit > 0.0:
                move_line.write({'debit': price})
    move._recompute_all()
    move.state = move_state
