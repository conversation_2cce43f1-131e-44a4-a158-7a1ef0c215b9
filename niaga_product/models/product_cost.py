# -*- coding: utf-8 -*-
# Copyright 2022 Altech Omega Andalan PT. - Imam <PERSON>
# License AGPL-3.0 or later (https://www.gnu.org/licenses/agpl.html).

from odoo import _, api, fields, models
from odoo.exceptions import ValidationError

import odoo.addons.decimal_precision as dp


class ProductCost(models.Model):
    _name = 'product.cost'
    _rec_name = 'product_tmpl_id'
    _order = 'product_tmpl_id, id desc'
    _description = 'Product Cost'

    def _set_product_tmpl_id(self):
        for cost in self:
            if cost.product_id:
                cost.product_tmpl_id = cost.product_id.product_tmpl_id.id

    product_tmpl_id = fields.Many2one(
        'product.template', string='Product', index=True, ondelete='cascade'
    )
    product_id = fields.Many2one(
        related='product_tmpl_id.product_variant_id',
        inverse='_set_product_tmpl_id',
        index=True,
        store=True,
        ondelete='cascade',
    )
    currency_id = fields.Many2one(
        'res.currency',
        string='Currency',
        default=lambda self: self.env.user.company_id.currency_id.id,
    )
    operating_unit_id = fields.Many2one(
        'operating.unit',
        string='Operating Unit',
        required=True,
        index=True,
        ondelete='cascade',
    )
    standard_price = fields.Monetary(
        string='Cost', digits=dp.get_precision('Product Price')
    )
    average_price = fields.Monetary(
        string='Average Cost', digits=dp.get_precision('Product Price')
    )

    @api.constrains('product_tmpl_id', 'operating_unit_id')
    def _check_unique_product_cost(self):
        if (
            self.search_count(
                [
                    ('product_tmpl_id', '=', self.product_tmpl_id.id),
                    ('operating_unit_id', '=', self.operating_unit_id.id),
                ]
            )
            > 1
        ):
            raise ValidationError(
                _('Not Allowed to create more than 1 Cost Price / Operating Unit')
            )

    @api.model
    def create(self, values):
        res = super().create(values)
        res._compute_pricelist_item()
        return res

    @api.multi
    def write(self, values):
        if values.get('operating_unit_id'):
            if values.get('operating_unit_id') != self.operating_unit_id.id:
                raise ValidationError(
                    _('Product Cost Operating Unit cannot be changed !')
                )
            elif (
                values.get('operating_unit_id') == self.operating_unit_id.id
                and len(values) == 1
            ):
                return False
        res = super().write(values)
        self._compute_pricelist_item()
        return res

    @api.multi
    def unlink(self):
        for cost in self:
            cost._compute_pricelist_item()
        return super().unlink()

    @api.multi
    def _compute_pricelist_item(self):
        for cost in self:
            self.env['product.pricelist.item'].search(
                [
                    ('product_tmpl_id', '=', cost.product_tmpl_id.id),
                    ('pricelist_id.operating_unit_id', '=', cost.operating_unit_id.id),
                ]
            )._compute_final_price()
