# -*- coding: utf-8 -*-
# Copyright 2022 Altech Omega Andalan PT. - Imam <PERSON>
# License AGPL-3.0 or later (https://www.gnu.org/licenses/agpl.html).

from odoo import _, fields, models
from odoo.exceptions import UserError


class ReturnPicking(models.TransientModel):
    _inherit = 'stock.return.picking'

    operating_unit_id = fields.Many2one(related='picking_id.operating_unit_id')
    location_id = fields.Many2one(
        domain="['|', ('usage', 'in', ['supplier', 'customer']),\
                '&',('usage', '=', 'internal'),\
                ('operating_unit_id', '=', operating_unit_id)]"
    )

    def _prepare_move_default_values(self, return_line, new_picking):
        vals = super(ReturnPicking, self)._prepare_move_default_values(
            return_line, new_picking
        )
        price_unit = return_line.move_id.price_unit
        if return_line.move_id.product_uom != return_line.product_id.uom_id:
            price_unit = return_line.move_id.product_uom._compute_price(
                return_line.move_id.price_unit, return_line.product_id.uom_id
            )
        vals['price_unit'] = price_unit
        vals['operating_unit_id'] = self.operating_unit_id.id
        vals['is_manual_return'] = True
        return vals

    def _create_returns(self):
        for return_move in self.product_return_moves.mapped('move_id'):
            return_move.move_dest_ids.filtered(
                lambda m: m.state not in ('done', 'cancel')
            )._do_unreserve()

        picking_type_id = (
            self.picking_id.picking_type_id.return_picking_type_id.id
            or self.picking_id.picking_type_id.id
        )

        if self._context.get('picking_type_id'):
            picking_type_id = self._context['picking_type_id']

        new_picking = self.picking_id.copy(
            {
                'move_lines': [],
                'picking_type_id': picking_type_id,
                'state': 'draft',
                'origin': _(f'Return of {self.picking_id.name}'),
                'location_id': self.picking_id.location_dest_id.id,
                'location_dest_id': self.location_id.id,
            }
        )
        new_picking.message_post_with_view(
            'mail.message_origin_link',
            values={'self': new_picking, 'origin': self.picking_id},
            subtype_id=self.env.ref('mail.mt_note').id,
        )
        returned_lines = 0
        for return_line in self.product_return_moves:
            if not return_line.move_id:
                raise UserError(
                    _(
                        'You have manually created product lines, \
                        please delete them to proceed'
                    )
                )

            if return_line.quantity:
                returned_lines += 1
                vals = self._prepare_move_default_values(return_line, new_picking)
                r = return_line.move_id.copy(vals)
                vals = {}

                move_orig_to_link = return_line.move_id.move_dest_ids.mapped(
                    'returned_move_ids'
                )
                move_dest_to_link = return_line.move_id.move_orig_ids.mapped(
                    'returned_move_ids'
                )
                vals['move_orig_ids'] = [
                    (4, m.id) for m in move_orig_to_link | return_line.move_id
                ]
                vals['move_dest_ids'] = [(4, m.id) for m in move_dest_to_link]
                r.write(vals)

        if not returned_lines:
            raise UserError(_('Please specify at least one non-zero quantity.'))

        new_picking.action_confirm()
        new_picking.action_assign()
        return new_picking.id, picking_type_id
