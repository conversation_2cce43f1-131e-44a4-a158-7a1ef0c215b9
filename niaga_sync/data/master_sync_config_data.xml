<?xml version="1.0" encoding="utf-8" ?>
<odoo noupdate="1">
    <data>

        <!-- Master Data Sync Configuration Parameters -->
        <!-- Note: Sensitive credentials are now stored in odoo.conf for security -->
        <!-- Only non-sensitive configuration is stored in database -->

        <!-- Webhook API Key for external integrations -->
        <record id="param_webhook_api_key" model="ir.config_parameter">
            <field name="key">sync.webhook.api_key</field>
            <field name="value">change_this_api_key_in_production</field>
        </record>

        <!-- Master Data Tables Configuration -->
        <!-- Comma-separated list of tables to include in master data replication -->
        <record id="param_master_data_tables" model="ir.config_parameter">
            <field name="key">niaga_sync.master_data_tables</field>
            <field
                name="value"
            >res_partner,res_partner_category,res_partner_title,res_partner_industry,res_partner_bank,res_country,res_country_state,res_currency,res_currency_rate,res_users,res_groups,res_company,res_bank,product_template,product_product,product_category,product_attribute,product_attribute_value,product_supplierinfo,product_pricelist,product_pricelist_item,product_uom_categ,product_uom,account_account,account_account_type,account_journal,account_tax,account_tax_group,account_fiscal_position,account_payment_term,account_analytic_account,stock_location,stock_warehouse,stock_picking_type,hr_employee,hr_department,hr_job,operating_unit,fleet_vehicle,fleet_vehicle_model,fleet_vehicle_model_brand,mrp_bom,mrp_bom_line,mrp_routing,mrp_workcenter,pos_config,pos_category,pos_membership,ir_sequence,ir_config_parameter</field>
        </record>

        <!-- Replication Set Name -->
        <record id="param_replication_set_name" model="ir.config_parameter">
            <field name="key">niaga_sync.replication_set_name</field>
            <field name="value">master_data_set</field>
        </record>

        <!-- Auto Setup Flag - Now configured in odoo.conf -->
        <!-- <record id="param_auto_setup_master_sync" model="ir.config_parameter">
            <field name="key">niaga_sync.auto_setup_master_sync</field>
            <field name="value">False</field>
        </record> -->

    </data>
</odoo>
