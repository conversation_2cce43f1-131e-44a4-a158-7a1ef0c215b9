# -*- coding: utf-8 -*-
# Copyright 2024 Altech Omega Andalan PT. - Imam <PERSON>
# License AGPL-3.0 or later (https://www.gnu.org/licenses/agpl.html).

import logging

import psycopg2

from odoo import SUPERUSER_ID, api
from odoo.tools import config

_logger = logging.getLogger(__name__)


def post_init_hook(cr, registry):
    """Post-installation hook to set up master data synchronization"""
    _logger.info('Checking master data synchronization setup...')

    try:
        # Check if auto setup is enabled (from odoo.conf)
        auto_setup = config.get('niaga_sync_auto_setup', 'False')

        if auto_setup == 'True':
            _logger.info(
                'Auto setup enabled, setting up master data synchronization...'
            )

            # Set up both source and target replication from target instance
            _setup_complete_replication(cr)

            # Create system parameter to track setup
            env = api.Environment(cr, SUPERUSER_ID, {})
            env['ir.config_parameter'].sudo().set_param(
                'niaga_sync.master_sync_setup', 'completed'
            )

            _logger.info('Master data synchronization setup completed successfully')
        else:
            _logger.info(
                'Auto setup disabled. Use the Master Data Sync Setup wizard to configure manually.'
            )
            env = api.Environment(cr, SUPERUSER_ID, {})
            env['ir.config_parameter'].sudo().set_param(
                'niaga_sync.master_sync_setup', 'manual_setup_required'
            )

    except Exception as e:
        _logger.error('Failed to set up master data synchronization: %s', str(e))
        # Don't fail the module installation, just log the error
        # Users can set up manually using the wizard


def _setup_complete_replication(cr):
    """Set up complete replication: source provider + target subscriber"""
    _logger.info('Setting up complete master data replication...')

    try:
        # First, set up source database (provider)
        _setup_source_provider()
        _logger.info('Source provider setup completed')

        # Then, set up target database (subscriber)
        _setup_pglogical_subscriber(cr)
        _logger.info('Target subscriber setup completed')

    except Exception as e:
        _logger.error('Failed to set up complete replication: %s', str(e))
        raise


def _setup_source_provider():
    """Set up pglogical provider on source database using remote connection"""
    _logger.info('Setting up source database provider...')

    # Get source database configuration from odoo.conf
    source_host = config.get('niaga_sync_source_host')
    source_port = config.get('niaga_sync_source_port', '5432')
    source_db = config.get('niaga_sync_source_db')
    source_user = config.get('niaga_sync_source_user', 'odoo')
    source_password = config.get('niaga_sync_source_password')

    # Validate required configuration
    if not all([source_host, source_db, source_password]):
        raise Exception(
            'Missing required source database configuration in odoo.conf. '
            'Please configure: niaga_sync_source_host, niaga_sync_source_db, niaga_sync_source_password'
        )

    # Create connection to source database
    source_conn = None
    source_cr = None

    try:
        _logger.info(
            f'Connecting to source database: {source_host}:{source_port}/{source_db}'
        )

        source_conn = psycopg2.connect(
            host=source_host,
            port=int(source_port),
            database=source_db,
            user=source_user,
            password=source_password,
        )
        source_conn.autocommit = True
        source_cr = source_conn.cursor()

        # Set up provider on source database
        _setup_pglogical_provider(
            source_cr, source_db, source_host, source_port, source_user
        )

        _logger.info('Source database provider setup completed successfully')

    except psycopg2.Error as e:
        _logger.error('PostgreSQL error connecting to source database: %s', str(e))
        raise Exception(f'Failed to connect to source database: {str(e)}')
    except Exception as e:
        _logger.error('Error setting up source provider: %s', str(e))
        raise
    finally:
        if source_cr:
            source_cr.close()
        if source_conn:
            source_conn.close()


def _setup_pglogical_provider(
    source_cr, source_db, source_host, source_port, source_user
):
    """Set up pglogical provider node and replication set on source database"""

    try:
        # Check if pglogical extension exists
        source_cr.execute("SELECT 1 FROM pg_extension WHERE extname = 'pglogical'")
        if not source_cr.fetchone():
            _logger.info('Installing pglogical extension on source database...')
            source_cr.execute('CREATE EXTENSION IF NOT EXISTS pglogical')
            _logger.info('pglogical extension installed on source database')

        # Create provider node
        node_name = f'source_node_{source_db}'
        dsn = f'host={source_host} port={source_port} dbname={source_db} user={source_user}'

        # Check if node already exists
        source_cr.execute(
            """
            SELECT 1 FROM pglogical.node WHERE node_name = %s
        """,
            (node_name,),
        )

        if not source_cr.fetchone():
            source_cr.execute(
                """
                SELECT pglogical.create_node(
                    node_name := %s,
                    dsn := %s
                )
            """,
                (node_name, dsn),
            )
            _logger.info('Created pglogical provider node: %s', node_name)
        else:
            _logger.info('Pglogical provider node already exists: %s', node_name)

        # Create replication set for master data
        replication_set = 'master_data_set'
        source_cr.execute(
            """
            SELECT 1 FROM pglogical.replication_set WHERE set_name = %s
        """,
            (replication_set,),
        )

        if not source_cr.fetchone():
            source_cr.execute(
                """
                SELECT pglogical.create_replication_set(%s)
            """,
                (replication_set,),
            )
            _logger.info('Created replication set: %s', replication_set)
        else:
            _logger.info('Replication set already exists: %s', replication_set)

        # Add master data tables to replication set
        master_tables = _get_master_data_tables()
        for table in master_tables:
            try:
                # Check if table exists
                source_cr.execute(
                    """
                    SELECT 1 FROM information_schema.tables
                    WHERE table_name = %s AND table_schema = 'public'
                """,
                    (table,),
                )

                if source_cr.fetchone():
                    # Check if table is already in replication set
                    source_cr.execute(
                        """
                        SELECT 1 FROM pglogical.replication_set_table
                        WHERE set_name = %s AND set_reloid = %s::regclass
                    """,
                        (replication_set, table),
                    )

                    if not source_cr.fetchone():
                        source_cr.execute(
                            """
                            SELECT pglogical.replication_set_add_table(%s, %s)
                        """,
                            (replication_set, table),
                        )
                        _logger.info('Added table %s to replication set', table)
                    else:
                        _logger.debug('Table %s already in replication set', table)
                else:
                    _logger.warning(
                        'Table %s does not exist on source, skipping', table
                    )

            except Exception as e:
                _logger.error(
                    'Failed to add table %s to replication: %s', table, str(e)
                )

        _logger.info('Source provider setup completed successfully')

    except psycopg2.Error as e:
        _logger.error('PostgreSQL error during source provider setup: %s', str(e))
        raise
    except Exception as e:
        _logger.error('Unexpected error during source provider setup: %s', str(e))
        raise


def _setup_pglogical_subscriber(cr):
    """Set up pglogical subscriber node and subscription"""

    # Get database configuration
    db_name = cr.dbname
    db_host = config.get('db_host', 'localhost')
    db_port = config.get('db_port', 5432)
    db_user = config.get('db_user', 'odoo')

    # Get source database configuration from odoo.conf
    source_host = config.get('niaga_sync_source_host')
    source_port = config.get('niaga_sync_source_port', '5432')
    source_db = config.get('niaga_sync_source_db')
    source_user = config.get('niaga_sync_source_user', 'odoo')

    # Validate required configuration
    if not all([source_host, source_db]):
        _logger.error(
            'Missing required source database configuration in odoo.conf. '
            'Please add: niaga_sync_source_host, niaga_sync_source_db'
        )
        raise Exception(
            'Source database configuration missing in odoo.conf. '
            'Please configure niaga_sync_source_host and niaga_sync_source_db.'
        )

    try:
        # Check if pglogical extension exists
        cr.execute("SELECT 1 FROM pg_extension WHERE extname = 'pglogical'")
        if not cr.fetchone():
            _logger.warning('pglogical extension not found. Installing...')
            try:
                cr.execute('CREATE EXTENSION IF NOT EXISTS pglogical')
                cr.commit()
                _logger.info('pglogical extension installed successfully')
            except Exception as e:
                _logger.error('Failed to install pglogical extension: %s', str(e))
                _logger.info(
                    'Please install pglogical manually: CREATE EXTENSION pglogical;'
                )
                return

        # Create subscriber node
        node_name = f'target_node_{db_name}'
        dsn = f'host={db_host} port={db_port} dbname={db_name} user={db_user}'

        # Check if node already exists
        cr.execute(
            """
            SELECT 1 FROM pglogical.node WHERE node_name = %s
        """,
            (node_name,),
        )

        if not cr.fetchone():
            cr.execute(
                """
                SELECT pglogical.create_node(
                    node_name := %s,
                    dsn := %s
                )
            """,
                (node_name, dsn),
            )
            _logger.info('Created pglogical subscriber node: %s', node_name)
        else:
            _logger.info('Pglogical subscriber node already exists: %s', node_name)

        # Create subscription to source database
        subscription_name = f'master_data_subscription_{db_name}'
        provider_dsn = f'host={source_host} port={source_port} dbname={source_db} user={source_user}'

        # Check if subscription already exists
        cr.execute(
            """
            SELECT 1 FROM pglogical.subscription WHERE subscription_name = %s
        """,
            (subscription_name,),
        )

        if not cr.fetchone():
            cr.execute(
                """
                SELECT pglogical.create_subscription(
                    subscription_name := %s,
                    provider_dsn := %s,
                    replication_sets := ARRAY['master_data_set'],
                    synchronize_structure := false,
                    synchronize_data := true
                )
            """,
                (subscription_name, provider_dsn),
            )
            _logger.info('Created pglogical subscription: %s', subscription_name)
        else:
            _logger.info('Pglogical subscription already exists: %s', subscription_name)

        cr.commit()

    except psycopg2.Error as e:
        _logger.error('PostgreSQL error during pglogical setup: %s', str(e))
        cr.rollback()
        raise
    except Exception as e:
        _logger.error('Unexpected error during pglogical setup: %s', str(e))
        cr.rollback()
        raise


def _get_master_data_tables():
    """Get list of master data tables to replicate based on actual database schema"""
    return [
        # Core master data
        'res_partner',
        'res_partner_category',
        'res_partner_title',
        'res_partner_industry',
        'res_partner_bank',
        'res_country',
        'res_country_state',
        'res_country_group',
        'res_currency',
        'res_currency_rate',
        'res_users',
        'res_groups',
        'res_groups_users_rel',
        'res_company',
        'res_company_users_rel',
        'res_bank',
        'res_lang',
        'res_config_settings',
        # Product master data
        'product_template',
        'product_product',
        'product_category',
        'product_attribute',
        'product_attribute_value',
        'product_attribute_line',
        'product_supplierinfo',
        'product_pricelist',
        'product_pricelist_item',
        'product_packaging',
        'product_barcode',
        # UoM master data (actual table names in your DB)
        'product_uom_categ',
        'product_uom',
        # Account master data
        'account_account',
        'account_account_type',
        'account_journal',
        'account_tax',
        'account_tax_group',
        'account_fiscal_position',
        'account_fiscal_position_tax',
        'account_fiscal_position_account',
        'account_payment_term',
        'account_payment_term_line',
        'account_analytic_account',
        'account_analytic_tag',
        'account_payment_method',
        'account_cash_rounding',
        'account_chart_template',
        # Stock master data
        'stock_location',
        'stock_warehouse',
        'stock_picking_type',
        'stock_location_path',
        'stock_incoterms',
        'stock_warehouse_orderpoint',
        # HR master data
        'hr_employee',
        'hr_employee_category',
        'hr_department',
        'hr_job',
        'hr_contract_type',
        'hr_holidays_status',
        'hr_payroll_structure',
        'hr_salary_rule',
        'hr_salary_rule_category',
        # Operating unit
        'operating_unit',
        'operating_unit_users_rel',
        # Fleet master data (confirmed in your DB)
        'fleet_vehicle',
        'fleet_vehicle_model',
        'fleet_vehicle_model_brand',
        'fleet_vehicle_state',
        'fleet_vehicle_tag',
        'fleet_service_type',
        # Manufacturing master data (confirmed in your DB)
        'mrp_bom',
        'mrp_bom_line',
        'mrp_routing',
        'mrp_routing_workcenter',
        'mrp_workcenter',
        'mrp_workcenter_productivity_loss',
        # POS master data (confirmed in your DB)
        'pos_config',
        'pos_category',
        'pos_membership',
        'pos_membership_rule',
        'pos_promotion',
        # System configuration
        'ir_sequence',
        'ir_sequence_date_range',
        'decimal_precision',
        'ir_model_fields',
        'ir_model_data',
        'ir_config_parameter',
        'ir_model_access',
        # Resource management
        'resource_calendar',
        'resource_calendar_attendance',
        'resource_calendar_leaves',
        'resource_resource',
        # Mail templates
        'mail_template',
        # Sales master data
        'sale_layout_category',
        # CRM master data
        'crm_team',
        # Additional master data tables found in your database
        'base_import_mapping',
        'base_import_tests_models_char',
    ]


def uninstall_hook(cr, registry):
    """Cleanup hook when module is uninstalled"""
    _logger.info('Cleaning up master data synchronization...')

    try:
        # Remove subscription
        db_name = cr.dbname
        subscription_name = f'master_data_subscription_{db_name}'

        cr.execute(
            """
            SELECT 1 FROM pglogical.subscription WHERE subscription_name = %s
        """,
            (subscription_name,),
        )

        if cr.fetchone():
            cr.execute(
                """
                SELECT pglogical.drop_subscription(%s)
            """,
                (subscription_name,),
            )
            _logger.info('Dropped pglogical subscription: %s', subscription_name)

        # Remove node
        node_name = f'target_node_{db_name}'
        cr.execute(
            """
            SELECT 1 FROM pglogical.node WHERE node_name = %s
        """,
            (node_name,),
        )

        if cr.fetchone():
            cr.execute(
                """
                SELECT pglogical.drop_node(%s)
            """,
                (node_name,),
            )
            _logger.info('Dropped pglogical node: %s', node_name)

        cr.commit()
        _logger.info('Master data synchronization cleanup completed')

    except Exception as e:
        _logger.error('Failed to cleanup master data synchronization: %s', str(e))
        # Don't fail uninstallation
