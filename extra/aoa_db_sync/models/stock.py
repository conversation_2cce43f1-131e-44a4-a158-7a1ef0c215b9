# -*- coding: utf-8 -*-
# Copyright 2022 Altech Omega Andalan PT. - Imam <PERSON>
# License AGPL-3.0 or later (https://www.gnu.org/licenses/agpl.html).

from odoo import _, api, models
from odoo.exceptions import ValidationError
from odoo.tools.float_utils import float_is_zero

from odoo.addons.stock.models import stock_warehouse

original_warehouse_create = stock_warehouse.Warehouse.create


def warehouse_new_create(self, values):
    get_param = self.env['ir.config_parameter'].sudo().get_param
    server_type = get_param('server.type', 'center')
    if server_type == 'center':
        return original_warehouse_create(self, values)
    return models.BaseModel.create(self, values)


stock_warehouse.Warehouse.create = warehouse_new_create


class Picking(models.Model):
    _inherit = ['stock.picking', 'db.sync.mixin.xml']
    _name = 'stock.picking'
    _sync_exclude_fields = [
        'picking_id',
        'product_qty',
        'quantity_done',
        'move_id',
        'move_line_nosuggest_ids',
        'account_move_ids',
        'show_operations',
        'show_details_visible',
        'show_reserved_availability',
        'picking_code',
        'product_type',
        'is_locked',
        'is_initial_demand_editable',
        'is_quantity_done_editable',
        'has_move_lines',
        'string_availability_info',
        'picking_partner_id',
        'reserved_availability',
        'availability',
        'has_tracking',
        'invoice_ids',
        'this_picking_create_backorder',
        'is_kontra',
        'kontra_bon_id',
        'has_scrap_move',
        'show_check_availability',
        'show_mark_as_todo',
        'show_validate',
        'show_lots_text',
        'is_driver_information_receipt',
        'is_driver_information_return',
        'is_vendor_return',
        'product_tmpl_id',
        'pos_line_ids',
        'price_change_ids',
        'pos_line_id',
    ]

    @api.model
    def create(self, values):
        if not self._server_center():
            user_id = self.env.user
            picking_type_id = self.env['stock.picking.type'].browse(
                values.get('picking_type_id')
            )
            Location = self.env['stock.location']
            location_id = Location.browse(values.get('location_id'))
            location_dest_id = Location.browse(values.get('location_dest_id'))
            if picking_type_id.operating_unit_id.is_main or (
                (
                    user_id.default_operating_unit_id
                    != picking_type_id.operating_unit_id
                    or user_id.default_operating_unit_id.id
                    != values.get('operating_unit_id')
                )
                and user_id.id != 1
            ):
                raise ValidationError(
                    _('Cannot create a Picking for other Operating Units')
                )

            if (
                location_id.usage == location_dest_id.usage == 'internal'
                and location_id.operating_unit_id != location_dest_id.operating_unit_id
            ):
                raise ValidationError(
                    _('Cannot create a Picking between Operating Units')
                )
        return super().create(values)

    def write(self, values):
        if self._server_center() and 'date_done' in values:
            for picking in self:
                if picking.date_done:
                    values.pop('date_done')
        return super().write(values)

    @api.multi
    def _set_tax_data(self):
        self.ensure_one()
        if self._server_center():
            return super()._set_tax_data()

    @api.multi
    def action_done(self):
        res = super().action_done()
        for picking in self.filtered(lambda p: not p.operating_unit_id.is_main):
            if (
                self._server_center()
                and self._context.get('is_sync')
                and picking.stock_return_request_id
            ):
                picking.returned_ids = [
                    (
                        6,
                        0,
                        picking.move_lines.mapped('returned_move_ids').mapped(
                            'picking_id.id'
                        ),
                    )
                ]
            elif not self._server_center():
                if not picking.xml_id:
                    picking.xml_id = picking._export_rows([['id']])[0][0]

                picking.is_synced = True
                self.env['db.sync.mixin']._generate_log(
                    'center',
                    'create',
                    picking.read()[0],
                    False,
                    picking._name,
                    picking.xml_id,
                )
        return res

    @api.multi
    def action_cancel(self):
        res = super().action_cancel()
        for picking in self.filtered(lambda p: not p.operating_unit_id.is_main):
            if self._server_center() and picking.is_synced:
                raise ValidationError(
                    _('Cannot cancel picking from Head Office Server')
                )
            elif not self._context.get('no_sync', False):
                if not picking.xml_id:
                    picking._set_xml_id()

                picking.is_synced = True
                self.env['db.sync.mixin']._generate_log(
                    'center',
                    'create',
                    picking.read()[0],
                    False,
                    picking._name,
                    picking.xml_id,
                )
        return res

    @api.multi
    def unlink(self):
        pickings = self.filtered(
            lambda r: r.state == 'cancel'
            and r.is_synced
            and not r.operating_unit_id.is_main
        )
        ctx = self._context
        for picking in pickings:
            if not self._server_center() and not any(
                [
                    ctx.get('is_sync', False),
                    ctx.get('no_sync', False),
                ]
            ):
                raise ValidationError(
                    _(f'This Picking {picking.name} cannot deleted in Branch server')
                )
            elif not ctx.get('no_sync'):
                self.env['db.sync.mixin']._generate_log(
                    'branch',
                    'unlink',
                    {},
                    picking.operating_unit_id.id,
                    picking._name,
                    picking.xml_id,
                )
        return super().unlink()


class PickingType(models.Model):
    _inherit = ['stock.picking.type', 'db.sync.mixin']
    _name = 'stock.picking.type'

    def create(self, values):
        if not self._server_center() and self._context.get('is_sync'):
            warehouse = self.env['stock.warehouse'].browse(values['warehouse_id'])
            sequence_data = warehouse._get_sequence_values()

            name = values['name']
            picking_type = ''
            if name == _('Receipts'):
                picking_type = 'in_type_id'
            elif name == _('Delivery Orders'):
                picking_type = 'out_type_id'
            elif name == _('Pack'):
                picking_type = 'pack_type_id'
            elif name == _('Pick'):
                picking_type = 'pick_type_id'
            elif name == _('Internal Transfers'):
                picking_type = 'int_type_id'
            elif name == _('PoS Orders'):
                picking_type = 'pos_type_id'
            elif name == _('PoS Orders Return'):
                picking_type = 'pos_return_type_id'
            elif name == _('Vendor Return'):
                picking_type = 'return_type_id'
            elif name == _('Manufacturing'):
                picking_type = 'manu_type_id'
            if name == _('Delivery Orders Return'):
                picking_type = 'sale_return_type_id'

            if picking_type:
                sequence = (
                    self.env['ir.sequence'].sudo().create(sequence_data[picking_type])
                )
                values['sequence_id'] = sequence.id
        return super().create(values)


class StockMove(models.Model):
    _inherit = ['stock.move', 'db.sync.mixin.xml']
    _name = 'stock.move'

    def _set_product_cost(self):
        return super(StockMove, self.with_context(no_sync=True))._set_product_cost()

    @api.multi
    def _set_main_product_cost(self):
        self.ensure_one()
        if not self._server_center():
            return False
        return super(
            StockMove, self.with_context(no_sync=True)
        )._set_main_product_cost()

    def _run_valuation(self, quantity=None):
        if self._server_center():
            return super()._run_valuation(quantity)
        if self._is_in():
            self = self.with_context(no_sync=True)
            if self.location_id.usage == 'supplier':
                self._set_vendor_pricelist()
                self._set_product_cost()
            elif self.production_id or self.location_id.usage == 'production':
                self._set_product_cost()
        return 0

    def _account_entry_move(self):
        if self._server_center():
            return super()._account_entry_move()
        return False

    def _action_done(self):
        moves_todo = super()._action_done()
        picking_id = moves_todo and moves_todo[0].picking_id or False
        if not picking_id:
            return moves_todo
        elif (
            self._server_center()
            and picking_id.location_id != picking_id.location_dest_id
            and self._context.get('is_sync')
        ):
            for move in moves_todo:
                domain = [
                    ('state', '=', 'done'),
                    ('qty_returnable', '>', 0),
                    ('product_id', '=', move.product_id.id),
                    ('operating_unit_id', '=', move.operating_unit_id.id),
                    ('location_dest_id', '=', move.location_id.id),
                ]
                returned_move_ids = self.search(domain, order='date desc, id desc')

                precision = move.product_uom.rounding
                qty_to_return = move.quantity_done
                for return_move in returned_move_ids:
                    qty_returnable = return_move.qty_returnable
                    if return_move.product_uom != move.product_uom:
                        qty_returnable = return_move.product_uom._compute_quantity(
                            return_move.qty_returnable, move.product_uom, round=False
                        )
                    qty_returnable = (
                        qty_returnable
                        if qty_to_return > qty_returnable
                        else qty_to_return
                    )
                    qty_to_return -= qty_returnable
                    return_move.origin_returned_move_id = move.id
                    if float_is_zero(qty_to_return, precision_rounding=precision):
                        break
        return moves_todo

    def product_price_update_before_done(self, forced_qty=None):
        if self._server_center():
            return super().product_price_update_before_done(forced_qty)
        return False


class StockMoveLine(models.Model):
    _inherit = 'stock.move.line'

    def _create_stock_summary(self):
        if self.env['db.sync.mixin']._server_center():
            return super()._create_stock_summary()
        return False


class Inventory(models.Model):
    _inherit = ['stock.inventory', 'db.sync.mixin.xml']
    _name = 'stock.inventory'
    _sync_exclude_fields = ['move_ids']

    @api.multi
    def unlink(self):
        inventories = self.filtered(
            lambda i: i.is_synced and not i.operating_unit_id.is_main
        )
        for inventory in inventories:
            if self._server_center() and not self._context.get('is_sync', False):
                raise ValidationError(
                    _(f'This inventory {inventory.name} cannot deleted in HO Server')
                )
            else:
                self.env['db.sync.mixin']._generate_log(
                    'center',
                    'unlink',
                    {},
                    None,
                    inventory._name,
                    inventory.xml_id,
                )
        return super().unlink()

    @api.constrains('operating_unit_id')
    def _check_operating_unit(self):
        for inventory in self:
            if (
                inventory._server_center()
                and not inventory.operating_unit_id.is_main
                and not self._context.get('is_sync')
            ):
                raise ValidationError(
                    _(
                        'In the Main Server you cannot create Inventory Adjustment '
                        'for Branch Operating Unit'
                    )
                )
            elif (
                not inventory._server_center()
                and inventory.operating_unit_id.is_main
                and not self._context.get('is_sync')
            ):
                raise ValidationError(
                    _(
                        'In the Branch Server you cannot create Inventory Adjustment '
                        'for Main Operating Unit'
                    )
                )

    def action_done(self):
        res = super().action_done()
        sync_mixin = self.env['db.sync.mixin']
        if not self._server_center():
            if not self.xml_id:
                self.xml_id = self._export_rows([['id']])[0][0]

            self.is_synced = True
            sync_mixin._generate_log(
                'center', 'create', self.read()[0], False, self._name, self.xml_id
            )
        return res


class InventoryLine(models.Model):
    _inherit = ['stock.inventory.line', 'db.sync.mixin.xml']
    _name = 'stock.inventory.line'


class StockWarehouse(models.Model):
    _inherit = ['stock.warehouse', 'db.sync.mixin']
    _name = 'stock.warehouse'


class StockLocation(models.Model):
    _inherit = ['stock.location', 'db.sync.mixin']
    _name = 'stock.location'


class StockLocationRoute(models.Model):
    _inherit = ['stock.location.route', 'db.sync.mixin']
    _name = 'stock.location.route'


class StockLocationPath(models.Model):
    _inherit = ['stock.location.path', 'db.sync.mixin']
    _name = 'stock.location.path'


class ProcurementRule(models.Model):
    _inherit = ['procurement.rule', 'db.sync.mixin']
    _name = 'procurement.rule'


class StockReturnRequest(models.Model):
    _inherit = ['stock.return.request', 'db.sync.mixin.xml']
    _name = 'stock.return.request'
    _sync_exclude_fields = ['returned_picking_ids', 'request_id']

    @api.multi
    def unlink(self):
        return_requests = self.filtered(
            lambda r: r.is_synced and not r.operating_unit_id.is_main
        )
        for request in return_requests:
            if self._server_center() and not self._context.get('is_sync', False):
                raise ValidationError(
                    _(f'This return request {request.name} cannot deleted in HO Server')
                )
            else:
                self.env['db.sync.mixin']._generate_log(
                    'center',
                    'unlink',
                    {},
                    None,
                    request._name,
                    request.xml_id,
                )
        return super().unlink()

    @api.constrains('operating_unit_id')
    def _check_operating_unit(self):
        for request in self:
            if (
                request._server_center()
                and not request.operating_unit_id.is_main
                and not self._context.get('is_sync')
            ):
                raise ValidationError(
                    _(
                        'Server Center cannot create '
                        'Return request for the Branch Operating unit'
                    )
                )
            elif (
                not request._server_center()
                and request.operating_unit_id.is_main
                and not self._context.get('is_sync')
            ):
                raise ValidationError(
                    _(
                        'Server Branch cannot create '
                        'Return request for the Main Operating unit'
                    )
                )

    def _action_confirm(self):
        self.ensure_one()
        if self._server_center() and self.operating_unit_id.is_main:
            self = self.with_context(no_sync=True)
        elif (
            not self._server_center()
            and not self.operating_unit_id.is_main
            and not self._context.get('is_sync')
        ):
            if not self.xml_id:
                self.xml_id = self._export_rows([['id']])[0][0]

            self.is_synced = True
            values = self.read()[0]
            values['state'] = 'confirmed'
            self.env['db.sync.mixin']._generate_log(
                'center', 'create', values, False, self._name, self.xml_id
            )
        return super(StockReturnRequest, self)._action_confirm()

    def _action_validate(self):
        res = super()._action_validate()
        if not self._server_center() and not self._context.get('is_sync'):
            sync_mixin = self.env['db.sync.mixin']
            sync_mixin._generate_log(
                'center', 'write', {'state': 'done'}, False, self._name, self.xml_id
            )
        if self._server_center() and not self.operating_unit_id.is_main:
            raise ValidationError(
                _(
                    'Server Center cannot Validate '
                    'Return request for Branch Operating unit'
                )
            )
        return res

    def action_cancel(self):
        res = super().action_cancel()
        if not self._server_center() and not self._context.get('is_sync'):
            sync_mixin = self.env['db.sync.mixin']
            sync_mixin._generate_log(
                'center', 'write', {'state': 'cancel'}, False, self._name, self.xml_id
            )
        return res


class StockSummary(models.Model):
    _inherit = 'stock.summary'

    @api.model
    def create(self, values):
        sync_mixin = self.env['db.sync.mixin']
        if not sync_mixin._server_center():
            return
        return super().create(values)

    def write(self, values):
        sync_mixin = self.env['db.sync.mixin']
        if not sync_mixin._server_center():
            return
        return super().write(values)

    def unlink(self):
        sync_mixin = self.env['db.sync.mixin']
        if not sync_mixin._server_center():
            return
        return super().unlink()


class StockPriceChange(models.Model):
    _inherit = 'stock.price.change'

    def _create_from_move(self, move, product_cost, standard_price):
        if self.env['db.sync.mixin']._server_center():
            return False
        return super()._create_from_move(move, product_cost, standard_price)

    @api.one
    def _update_sale_price_new(self, operating_unit_id=False):
        if self.env['db.sync.mixin']._server_center():
            return False
        return super()._update_sale_price_new(operating_unit_id)


class StockReplenishment(models.Model):
    _inherit = 'stock.replenishment'

    @api.model
    def default_get(self, fields_list):
        defaults = super().default_get(fields_list)
        operating_unit = self.env['db.sync.server']._get_operating_unit()
        defaults['operating_unit_id'] = operating_unit.id
        return defaults

    @api.multi
    def _sync_validation(self):
        for rec in self:
            if self.env[
                'db.sync.server'
            ]._get_operating_unit() != rec.operating_unit_id and not self._context.get(
                'no_sync', False
            ):
                raise ValidationError(
                    _('Cannot process this action with different Operating Unit/Branch')
                )

    @api.model
    def create(self, values):
        result = super().create(values)
        result._sync_validation()
        return result

    def write(self, values):
        self._sync_validation()
        return super().write(values)

    def unlink(self):
        self._sync_validation()
        return super().unlink()


class RunStockReplenishment(models.TransientModel):
    _inherit = 'run.stock.replenishment'

    @api.model
    def default_get(self, fields_list):
        defaults = super().default_get(fields_list)
        operating_unit = self.env['db.sync.server']._get_operating_unit()
        defaults['operating_unit_id'] = operating_unit.id
        return defaults

    @api.multi
    def generate_replenishment(self):
        if self.env['db.sync.server']._get_operating_unit() != self.operating_unit_id:
            raise ValidationError(
                _('Cannot generate Store Replenishment for different Operating Unit')
            )
        return super().generate_replenishment()


class StockPosition(models.Model):
    _inherit = 'stock.position'

    @api.model
    def default_get(self, fields_list):
        defaults = super().default_get(fields_list)
        operating_unit = self.env['db.sync.server']._get_operating_unit()
        defaults['operating_unit_id'] = operating_unit.id
        return defaults

    @api.multi
    def _sync_validation(self):
        for rec in self:
            if self.env[
                'db.sync.server'
            ]._get_operating_unit() != rec.operating_unit_id and not self._context.get(
                'no_sync', False
            ):
                raise ValidationError(
                    _('Cannot process this action with different Operating Unit/Branch')
                )

    @api.model
    def create(self, values):
        result = super().create(values)
        result._sync_validation()
        return result

    def write(self, values):
        self._sync_validation()
        return super().write(values)

    def unlink(self):
        self._sync_validation()
        return super().unlink()
